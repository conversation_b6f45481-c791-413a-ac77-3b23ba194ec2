<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "sms_log".
 *
 * @property int $id id;主键id
 * @property string $add_time 创建时间
 * @property int $status 状态1成功,0失败
 * @property string $mobile_code 手机号区号
 * @property string $mobile 手机号
 * @property string $content 发送内容
 * @property int $type 类型, 1注册登录
 * @property string $reason 发送失败原因
 * @property string $ext_params 扩展参数
 * @property string $sid 运营商sid(可用于回调查找)
 * @property int $receive_status 回调状态,0未回调，1回调告知成功，-1，回调告知失败)
 * @property string $receive_data 回调数据，json格式
 */
class SmsLog extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'sms_log';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_time'], 'safe'],
            [['status', 'type', 'receive_status'], 'integer'],
            [['mobile_code', 'mobile'], 'string', 'max' => 16],
            [['content'], 'string', 'max' => 512],
            [['reason', 'ext_params'], 'string', 'max' => 255],
            [['sid'], 'string', 'max' => 128],
            [['receive_data'], 'string', 'max' => 1024],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_time' => 'Add Time',
            'status' => 'Status',
            'mobile_code' => 'Mobile Code',
            'mobile' => 'Mobile',
            'content' => 'Content',
            'type' => 'Type',
            'reason' => 'Reason',
            'ext_params' => 'Ext Params',
            'sid' => 'Sid',
            'receive_status' => 'Receive Status',
            'receive_data' => 'Receive Data',
        ];
    }
}
