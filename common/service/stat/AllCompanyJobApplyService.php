<?php

namespace common\service\stat;

use common\base\models\BaseAnnouncement;
use common\base\models\BaseCompany;
use common\base\models\BaseJob;
use common\base\models\BaseJobApply;
use common\base\models\BaseJobApplyRecord;
use common\base\models\BaseOffSiteJobApply;
use common\base\models\BaseResume;
use common\base\models\BaseResumeTag;
use common\helpers\ArrayHelper;
use common\helpers\TimeHelper;
use common\helpers\UrlHelper;
use common\helpers\UUIDHelper;
use common\libs\Excel;
use common\libs\WxWork;

class AllCompanyJobApplyService extends BaseService
{

    const TimeDimDay = 7;

    private $beginTime;
    private $endTime;
    private $beginDay;
    private $endDay;
    private $url;

    // 主要是统计全部的公司的投递情况,定时推送给对于的人员

    // 序号	时间	职位	公告	公告链接	职位链接	单位名称	单位类型	投递人	注册时间
    public function run()
    {
        $this->setTime();
        $this->getData();
        $this->send();
    }

    private function setTime()
    {
        // 设置时间,首先是昨天的
        $yesterday = date('Y-m-d', strtotime('-1 day'));
        // 7天前
        $before7Day = date('Y-m-d', strtotime('-' . self::TimeDimDay . ' day'));

        $this->beginDay = $before7Day;
        $this->endDay   = $yesterday;

        $this->beginTime = TimeHelper::dayToBeginTime($before7Day);

        $this->endTime = TimeHelper::dayToEndTime($yesterday);
    }

    private function getData()
    {
        //        $onlineJobApply = BaseJobApply::find()
        //            ->alias('a')
        //            ->select([
        //                'a.id',
        //                'b.company_id',
        //                'b.announcement_id',
        //                'd.title as announcement_name',
        //                'a.job_id',
        //                'b.name as job_name',
        //                'a.add_time',
        //                'c.full_name',
        //                'd.title',
        //                'e.name',
        //                'e.add_time as register_time',
        //                'is_cooperation',
        //                'jar.delivery_way',
        //            ])
        //            ->innerJoin(['b' => BaseJob::tableName()], 'a.job_id = b.id')
        //            ->innerJoin(['c' => BaseCompany::tableName()], 'b.company_id = c.id')
        //            ->leftJoin(['jar' => BaseJobApplyRecord::tableName()], 'a.id = jar.apply_id')
        //            ->leftJoin(['d' => BaseAnnouncement::tableName()], 'b.announcement_id = d.id')
        //            ->innerJoin(['e' => BaseResume::tableName()], 'e.id = a.resume_id')
        //            ->where([
        //                '>=',
        //                'a.add_time',
        //                $this->beginTime,
        //            ])
        //            ->andWhere([
        //                '<=',
        //                'a.add_time',
        //                $this->endTime,
        //            ])
        //            ->orderBy('c.is_cooperation,c.id desc,d.id')
        //            ->asArray()
        //            ->all();
        //
        //        $offlineJobApply = BaseOffSiteJobApply::find()
        //            ->alias('a')
        //            ->select([
        //                'a.id',
        //                'b.company_id',
        //                'b.announcement_id',
        //                'd.title as announcement_name',
        //                'a.job_id',
        //                'b.name as job_name',
        //                'a.add_time',
        //                'c.full_name',
        //                'd.title',
        //                'e.name',
        //                'e.add_time as register_time',
        //                'is_cooperation',
        //                'jar.delivery_way',
        //            ])
        //            ->innerJoin(['b' => BaseJob::tableName()], 'a.job_id = b.id')
        //            ->innerJoin(['c' => BaseCompany::tableName()], 'b.company_id = c.id')
        //            ->leftJoin(['jar' => BaseJobApplyRecord::tableName()], 'a.id = jar.apply_site_id')
        //            ->leftJoin(['d' => BaseAnnouncement::tableName()], 'b.announcement_id = d.id')
        //            ->innerJoin(['e' => BaseResume::tableName()], 'e.id = a.resume_id')
        //            ->where([
        //                '>=',
        //                'a.add_time',
        //                $this->beginTime,
        //            ])
        //            ->andWhere([
        //                '<=',
        //                'a.add_time',
        //                $this->endTime,
        //            ])
        //            ->orderBy('c.is_cooperation,c.id desc,d.id')
        //            ->asArray()
        //            ->all();
        //
        //        // 合并两个数组
        //        $data = array_merge($onlineJobApply, $offlineJobApply);
        //
        //        $list = ArrayHelper::arraySorts($data, [
        //            'is_cooperation'  => "asc",
        //            'company_id'      => "asc",
        //            'announcement_id' => "asc",
        //        ]);
        $list  = BaseJobApplyRecord::find()
            ->alias('jar')
            ->select([
                'jar.company_id',
                'jar.announcement_id',
                'd.title as announcement_name',
                'jar.job_id',
                'b.name as job_name',
                'jar.add_time',
                'c.full_name',
                'd.title',
                'e.name',
                'e.add_time as register_time',
                'c.is_cooperation',
                'jar.delivery_way',
                'jar.resume_id',
            ])
            ->innerJoin(['b' => BaseJob::tableName()], 'jar.job_id = b.id')
            ->innerJoin(['c' => BaseCompany::tableName()], 'b.company_id = c.id')
            ->leftJoin(['ja' => BaseJobApply::tableName()], 'ja.id = jar.apply_id')
            ->leftJoin(['o' => BaseOffSiteJobApply::tableName()], 'o.id = jar.apply_site_id')
            ->leftJoin(['d' => BaseAnnouncement::tableName()], 'b.announcement_id = d.id')
            ->innerJoin(['e' => BaseResume::tableName()], 'e.id = jar.resume_id')
            ->where([
                '>=',
                'jar.add_time',
                $this->beginTime,
            ])
            ->andWhere([
                '<=',
                'jar.add_time',
                $this->endTime,
            ])
            ->orderBy('c.is_cooperation,jar.company_id desc,jar.announcement_id')
            ->asArray()
            ->all();
        $excel = new Excel();

        $headers = [
            '序号',
            '时间',
            '日期',
            '职位',
            '公告',
            '公告链接',
            '职位链接',
            '单位名称',
            '单位类型',
            '投递人',
            '求职者id',
            '注册时间',
            '注册日期',
            '投递方式',
            '人才标签',
        ];

        $data = [];

        $params = \Yii::$app->params['baiduZZ'];
        $pcSite = $params['pcSite'];

        foreach ($list as $k => $val) {
            $num = $k + 1;
            // 获取人才标签
            $resumeTagsList = BaseResumeTag::getTagList($val['resume_id']);
            $resumeTags     = implode('、', $resumeTagsList);
            $data[]         = [
                $num,
                $val['add_time'],
                substr($val['add_time'], 0, 10),
                $val['job_name'],
                $val['announcement_name'],
                'http://' . $pcSite . UrlHelper::createAnnouncementDetailPath($val['announcement_id']),
                'http://' . $pcSite . UrlHelper::createJobDetailPath($val['job_id']),
                $val['full_name'],
                $val['is_cooperation'] == 1 ? '合作单位' : '非合作单位',
                $val['name'],
                UUIDHelper::encrypt(UUIDHelper::TYPE_PERSON, $val['resume_id']),
                $val['register_time'],
                substr($val['register_time'], 0, 10),
                BaseJobApplyRecord::DELIVERY_WAY_NAME[$val['delivery_way']],
                $resumeTags,
            ];
        }

        $rs = $excel->export($data, $headers, CUR_DATE . '_投递统计日报_' . CUR_TIMESTAMP);

        $this->url = $rs;
    }

    private function send()
    {
        $wxWork = WxWork::getInstance();
        // 找到操作人的信息

        $content = $this->url;

        $wxWork->cardToOperation('投递日报' . CUR_DATETIME,
            '过去七天(' . $this->beginDay . '到' . $this->endDay . ')的投递数据,点击下载', $content);
    }

}
