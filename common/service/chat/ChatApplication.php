<?php

namespace common\service\chat;

// 全部方法由此统一对外暴露
use common\base\models\BaseCompanyMemberInfo;
use common\base\models\BaseCompanyPackageChangeDetailLog;
use common\base\models\BaseCompanyPackageChangeLog;
use common\base\models\BaseCompanyPackageConfig;
use common\base\models\BaseResume;
use common\components\MessageException;
use common\helpers\DebugHelper;
use common\service\companyPackage\CompanyPackageApplication;
use common\service\companyPackage\ConsumeService;
use common\service\messageCenter\MessageCenterApplication;
use yii\db\conditions\AndCondition;
use yii\db\Exception;

class ChatApplication
{

    static $instance;

    public static function getInstance()
    {
        if (!self::$instance) {
            self::$instance = new self();
        }

        return self::$instance;
    }

    public function resumeCreateRoom($params)
    {
        return (new CreateService())->resumeCreate($params);
    }

    // public function companyCheckRoom($params)
    // {
    //     return (new CreateService())->companyCheck($params);
    // }

    public function companyCreateRoom($params)
    {
        $service   = new CreateService();
        $resumeIds = explode(',', $params['resumeId']);
        //判断简历ID的数量
        if (count($resumeIds) > 1) {
            // 这里有可能是多个,多个就去调用batch
            $data = $service->companyCreateBatch($params);
        } else {
            $data = $service->companyCreate($params);
        }

        // 追加上次记住的短信数量和套餐配置数量
        $data['isRememberSmsChat'] = $service->getCompanyMemberIsRememberSmsChat();
        $data['packageDetail']     = BaseCompanyPackageConfig::getCompanyPackageConfigDetail($service->companyId);
        // 2.3.2 成功需要将短信发送
        $sendSmsResumeList = [];
        if ($data['create_room_res'] && $params['isRememberSmsChat'] == BaseCompanyMemberInfo::IS_REMEMBER_YES) {
            // 先去除非国内手机
            $resumeIds = BaseResume::getChinaMobileResume($resumeIds);
            if (count($resumeIds) > $data['packageDetail']['sms_amount']) {
                throw new MessageException('短信余量不足！');
            }

            // 如果没有直接成功
            if (!$resumeIds) {
                return true;
            }

            // 追加队列
            foreach ($resumeIds as $resumeId) {
                $messageData         = (new MessageCenterApplication())->jobChatInvitation($resumeId,
                    ['is_create_room' => $service->isCreateRoomRes]);
                $sendSmsResumeList[] = [
                    'smsId'    => $messageData['smsId'] ?? 0,
                    'resumeId' => $resumeId,
                ];
            }

            // 扣除短信
            $smsLogMsg   = '直聊通知；简历ID：' . implode('、', $resumeIds);
            $model       = CompanyPackageApplication::getInstance();
            $changeLogId = $model->setSmsAmount(ConsumeService::HANDLE_TYPE_SMS_SEND, $service->companyId,
                count($resumeIds), $smsLogMsg, CompanyPackageApplication::ALGORITHM_CUT);

            foreach ($sendSmsResumeList as $sendSmsResume) {
                BaseCompanyPackageChangeDetailLog::saveDetail([
                    'type'          => BaseCompanyPackageChangeDetailLog::TYPE_COMPANY_JOB_INVITE_CHAT,
                    'company_id'    => $params['companyId'],
                    'resume_id'     => $sendSmsResume['resumeId'],
                    'resource_id'   => $sendSmsResume['smsId'],
                    'change_log_id' => $changeLogId,
                    'job_id'        => $params['jobId'],
                    'member_id'     => $params['companyMemberId'],
                ]);
            }
        }

        return $data;
    }

    public function companychangeJob($params)
    {
        return (new CreateService())->companyChangeJob($params);
    }

    public function resumechangeJob($params)
    {
        return (new CreateService())->resumeChangeJob($params);
    }

    public function resumeGetRoomInfo($resumeId, $chatId)
    {
    }

    public function companyGetRoomInfo($companyMemberId, $chatId)
    {
    }

    public function companyGetRoomResumeInfo($chatId, $companyMemberId)
    {
        return (new SearchService())->companyGetRoomResumeInfo($chatId, $companyMemberId);
    }

    public function resumeUpload($memberId, $chatId)
    {
        return (new FileService())->upload($memberId, $chatId);
    }

    public function download($memberId, $messageId)
    {
        return (new FileService())->download($memberId, $messageId);
    }

    public function getCommonPhrase($memberId)
    {
        return (new CommonPhrase())->getList($memberId);
    }

    public function deleteCommonPhrase($memberId, $id)
    {
        return (new CommonPhrase())->delete($memberId, $id);
    }

    public function editCommonPhrase($memberId, $content, $id = '')
    {
        return (new CommonPhrase())->edit($memberId, $content, $id);
    }

    /**
     * 获取打招呼语列表
     * @param $memberId
     * @return array
     */
    public function getCommonGreeting($memberId)
    {
        return (new CommonGreeting())->getList($memberId);
    }

    /**
     * 删除打招呼语
     * @param $memberId
     * @param $id
     * @return false|int
     * @throws \Exception
     */
    public function deleteCommonGreeting($memberId, $id)
    {
        return (new CommonGreeting())->delete($memberId, $id);
    }

    /**
     * 添加或编辑打招呼语
     * @param     $memberId
     * @param     $content
     * @param int $id
     * @return bool
     * @throws \Exception
     */
    public function AddOrEditCommonGreeting($memberId, $content, $id = 0)
    {
        return (new CommonGreeting())->addOrEdit($memberId, $content, $id);
    }

    /**
     * 修改打招呼语配置开关
     * @param $memberId
     * @return bool
     * @throws \Exception
     */
    public function EditIsGreetingCommonGreeting($memberId)
    {
        return (new CommonGreeting())->editIsGreeting($memberId);
    }

    /**
     * 修改默认打招呼语
     * @param $memberId
     * @param $id
     * @param $type
     * @return mixed
     */
    public function EditDefaultGreetingCommonGreeting($memberId, $id, $type)
    {
        return (new CommonGreeting())->editDefaultGreeting($memberId, $id, $type);
    }

    /**
     * 求职者发送附件前置判断
     * @param $jobId
     * @param $chatId
     * @return bool|string[]
     * @throws \yii\web\NotFoundHttpException
     */
    public function checkPersonRequestFile($jobId, $chatId)
    {
        return (new CheckService())->checkPersonRequestFile($jobId, $chatId);
    }

    /**
     * 单位附件补充前置判断
     * @param $jobId
     * @param $chatId
     * @return bool|string[]
     * @throws \yii\web\NotFoundHttpException
     */
    public function checkCompanyRequestFile($jobId, $chatId)
    {
        return (new CheckService())->checkCompanyRequestFile($jobId, $chatId);
    }

    public function checkCompanyInviteApply($companyMemberId)
    {
        return (new CheckService())->checkCompanyInviteApply($companyMemberId);
    }

    /**
     * 获取求职者聊天列表
     * @param        $memberId
     * @param string $keyword
     * @param string $readStatus
     * @return array|\yii\db\ActiveRecord[]
     */
    public function getPersonChatList($memberId, string $keyword = '', string $readStatus = '')
    {
        return (new InfoService())->getPersonChatList($memberId, $keyword, $readStatus);
    }

    /**
     * 获取单位聊天列表
     * @param $memberId
     * @param $keyword
     * @param $readStatus
     * @return array|\yii\db\ActiveRecord[]
     */
    public function getCompanyChatList($memberId, $type = '', $keyword = '', $readStatus = '', $page = '')
    {
        return (new InfoService())->getCompanyChatList($memberId, $type, $keyword, $readStatus, $page);
    }

    /**
     * 获取聊天室信息，头部展示
     * @param $chatId
     * @param $memberId
     * @return array
     */
    public function getChatInfo($chatId, $memberId)
    {
        return (new InfoService())->getChatInfo($chatId, $memberId);
    }

    /**
     * 获取历史聊天记录
     * @param $chatId
     * @param $messageId
     * @param $pageLimit
     * @return array|\yii\db\ActiveRecord[]
     */
    public function getHistoryList($chatId, $memberId, $messageId = '', $pageLimit = '')
    {
        return (new InfoService())->getHistoryList($chatId, $memberId, $messageId, $pageLimit);
    }

    /**
     * 获取历史聊天记录(单个
     * @param $chatId
     * @param $messageId
     * @return array|\yii\db\ActiveRecord[]
     */
    public function getHistoryOne($messageId, $memberId)
    {
        return (new InfoService())->getHistoryOne($messageId, $memberId);
    }

    /**
     * 获取协同职位列表
     * @param $chatId
     * @param $memberId
     * @return array|\yii\db\ActiveRecord[]
     */
    public function getChatJobList($chatId, $memberId)
    {
        return (new InfoService())->getChatJobList($chatId, $memberId);
    }

    /**
     * 设置聊天室置顶
     * @param $chatId
     * @param $memberId
     * @return void
     * @throws \yii\base\Exception
     */
    public function setTop($chatId, $memberId)
    {
        (new InfoService())->setTop($chatId, $memberId);
    }

    /**
     * 删除聊天室
     * @param $chatId
     * @param $memberId
     * @return void
     */
    public function delRoom($chatId, $memberId)
    {
        (new InfoService())->delRoom($chatId, $memberId);
    }

    /**
     * 修复聊天室历史消息
     */
    public function fixChatSession($chatId)
    {
        (new Fix())->chatSession($chatId);
    }

    /**
     * 聊天室企业推送短信通知用户
     */
    public function chatRoomSendSmsToResume($isOnlyCheck, $companyId, $resumeId, $memberId)
    {
        $packageDetail = BaseCompanyPackageConfig::getCompanyPackageConfigDetail($companyId);
        if ($packageDetail['sms_amount'] < 1) {
            throw new MessageException('您的短信余量为0，请联系平台客服购买。');
        }
        $resumeIds = BaseResume::getChinaMobileResume([$resumeId]);
        if (!$resumeIds) {
            throw new MessageException('对不起，当前暂不支持向海外手机号发送短信。');
        }

        $detailWhere  = [
            [
                '=',
                'company_id',
                $companyId,
            ],
            [
                '=',
                'resume_id',
                $resumeId,
            ],
            [
                '=',
                'member_id',
                $memberId,
            ],
            [
                '=',
                'type',
                BaseCompanyPackageChangeDetailLog::TYPE_COMPANY_JOB_INVITE_CHAT_TIPS,
            ],
            [
                '>',
                'add_time',
                date('Y-m-d 00:00:00', time()),
            ],
        ];
        $checkHasSend = BaseCompanyPackageChangeDetailLog::find()
            ->where(new AndCondition($detailWhere))
            ->exists();

        if ($checkHasSend) {
            throw new MessageException('您今日已向人才发送过短信哦，请耐心等待~');
        }

        if ($isOnlyCheck == 1) {
            return [
                'passStep' => 1,
            ];
        }

        // 追加队列
        $messageData = (new MessageCenterApplication())->chatRoomPromptMember($resumeId);

        // 扣除短信
        $smsLogMsg                 = '直聊通知；简历ID：' . implode('、', $resumeIds);
        $companyPackageApplication = CompanyPackageApplication::getInstance();
        $changeLogId               = $companyPackageApplication->setSmsAmount(ConsumeService::HANDLE_TYPE_SMS_SEND,
            $companyId, count($resumeIds), $smsLogMsg, CompanyPackageApplication::ALGORITHM_CUT);
        BaseCompanyPackageChangeDetailLog::saveDetail([
            'type'          => BaseCompanyPackageChangeDetailLog::TYPE_COMPANY_JOB_INVITE_CHAT_TIPS,
            'company_id'    => $companyId,
            'resume_id'     => $resumeId,
            'resource_id'   => $messageData['smsId'],
            'change_log_id' => $changeLogId,
            'member_id'     => $memberId,
        ]);

        return [
            'passStep' => 2,
        ];
    }
}
