<?php

namespace common\service\messageCenter;

use common\base\models\BaseJob;
use common\base\models\BaseMember;
use common\base\models\BaseMemberMessage;
use common\base\models\BaseResume;
use common\base\models\BaseResumeLibraryInviteLog;
use common\base\models\BaseResumeSetting;
use common\base\models\BaseResumeWxBind;
use common\libs\SmsQueue;
use Yii;

class JobInvitationService extends BaseService
{

    /**
     * @throws \Exception
     */
    public function run($inviteId, $isSendEmail, $isSendSms = 0)
    {
        $invite = BaseResumeLibraryInviteLog::findOne(['id' => $inviteId]);

        if (!$invite) {
            return false;
        }

        $resumeId             = $invite->resume_id;
        $memberId             = BaseResume::findOneVal(['id' => $resumeId], 'member_id');
        $this->onSiteMemberId = $memberId;
        $jobId                = $invite->job_id;
        $remark               = $invite->remark;

        $this->key = self::JOB_INVITATION_KEY;
        $this->setConfig();

        $job = BaseJob::getMailJob($jobId);

        if ($isSendEmail || $isSendSms) {
            $memberInfo = BaseMember::find()
                ->where(['id' => $memberId])
                ->select([
                    'email',
                    'mobile',
                    'mobile_code',
                ])
                ->asArray()
                ->one();
        }
        //todo 这里生成页面-邮件
        if ($isSendEmail) {
            $this->email = $memberInfo['email'] ?? '';

            // $tags        = '';
            // $hoverTags   = '';
            // if ($job['welfareTagArr']) {
            //     foreach ($job['welfareTagArr'] as $item) {
            //         $tags = $tags . ' <span class="tag-primary">' . $item . '</span>';
            //     }
            // }
            // if ($job['isWelfare'] == 1) {
            //     $hoverTags = '<div class="tag-collect">
            //                         <span class="tag-primary">···</span>
            //                         <div class="hover-area">';
            //     foreach ($job['welfareTagAll'] as $item) {
            //         $hoverTags = $hoverTags . ' <span class="tag-primary">' . $item . '</span>';
            //     }
            //     $hoverTags = $hoverTags . '</div>
            //                     </div>';
            // }
            //
            // $remarkLog = '';
            // if ($remark) {
            //     $remarkLog = '<div>备注：' . $remark . '</div>';
            // }
            //
            // $memberName = BaseResume::findOneVal(['member_id' => $memberId], 'name');
            // $html       = '<div class="container">
            //             <div class="email-body">
            //         <div class="hi">尊敬的' . $memberName . '，您好：</div>
            //         <div class="email-content">我是<a href="' . $job['companyLink'] . '"
            //                 target="_blank">' . $job['companyName'] . '</a>' . $job['department'] . '
            //             招聘负责人，诚邀您投递我单位 “<a href="' . $job['jobLink'] . '" target="_blank">' . $job['jobName'] . '</a>“
            //             岗位。如果您对此岗位感兴趣，欢迎登录高校人才网查看并投递，如您身边有适合此职位的其他人才，也欢迎推荐。谢谢！</div>
            //             ' . $remarkLog . '
            //         <div class="email-footer">' . $job['companyName'] . '</div>
            //     </div>';
            //
            // $html = $html . '<div class="job-invite-content" href="" target="_blank">
            //         <a class="job-detail" href="' . $job['jobLink'] . '" target="_blank">
            //             <div class="title">职位邀请</div>
            //             <div class="top">
            //                 <div class="job-name">' . $job['jobName'] . '</div>
            //                 <div class="job-date">' . $job['refreshDate'] . '发布</div>
            //             </div>
            //             <div class="job-info">
            //                 <div class="job-require">' . $job['education'] . '丨' . $job['areaName'] . ' 丨' . $job['amount'] . '人</div>
            //                 <div class="job-welfare">
            //                     ' . $tags . $hoverTags . '
            //                 </div>
            //             </div>
            //            ';
            //
            // if ($job['requirement']) {
            //     $html = $html . ' <div class="work-require">
            //                 <div class="require">任职要求</div>
            //                 <pre>' . $job['requirement'] . '
            //                 </pre>
            //             </div>
            //             <div class="more">查看更多</div>';
            // }
            //
            // $html = $html . ' </a>
            //             <a class="accept-btn" href="' . $job['jobLink'] . '?from=email" target="_blank">接受邀请</a>
            //         </div>
            //     </div>';
            //
            // $this->emailData    = ['html' => $html];

            // 内容迁移到发送邮件的时候
            $this->emailContent = [
                'companyName' => $job['companyName'],
                'jobName'     => $job['jobName'],
                'resumeId'    => $resumeId,
                'jobId'       => $jobId,
                'remark'      => $remark,
            ];
        }

        if ($isSendSms) {
            $this->mobile     = $memberInfo['mobile'] ?? '';
            $this->mobileCode = $memberInfo['mobile_code'] ?? '';
            // 把一些额外的信息给到短信里面
            $this->smsExtra = json_encode(['inviteId' => $inviteId]);
        }
        // 微信消息推送
        $this->setBind($resumeId);
        //微信通知数据
        if (in_array(self::TYPE_CHANNEL_WECHAT, $this->channel)) {
            $env = Yii::$app->params['environment'];
            if ($env == 'prod') {
                $templateId = Yii::$app->params['wx']['privateTemplates'][$this->key]['prod'];
            } else {
                $templateId = Yii::$app->params['wx']['privateTemplates'][$this->key]['gray'];
            }

            $resumeWxBind = BaseResumeWxBind::findOne([
                'resume_id'    => $resumeId,
                'is_subscribe' => BaseResumeWxBind::STATUS_ACTIVE,
            ]);
            $url          = Yii::$app->params['h5Host'] . '/job/detail/' . $jobId . '.html';
            $resumeName   = BaseResume::findOneVal(['id' => $resumeId], 'name');
            $title        = $resumeName . ',您好。' . $job['companyName'] . '对您的简历很感兴趣，邀请您投递简历，期待您的回复。';
            $this->wxData = [
                'first'        => [
                    'value' => $title,
                    'color' => '#0000FF',
                ],
                'keyword1'     => [
                    'value' => $job['jobName'],
                    'color' => '#37416b',
                ],
                'keyword2'     => [
                    'value' => $job['companyName'],
                    'color' => '#37416b',
                ],
                'keyword3'     => [
                    'value' => $job['areaName'],
                    'color' => '#37416b',
                ],
                'remark'       => [
                    'value' => $this->config['wxRemark'],
                    'color' => '#37416b',
                ],
                'openid'       => $resumeWxBind['openid'],
                'templateId'   => $templateId,
                'url'          => $url,
                'miniPagePath' => 'packages/job/detail/index?scene=' . http_build_query(['id' => $jobId]),
            ];
        }

        // 站内信
        if (!empty($remark)) {
            $remark = "邀请备注：$remark";
        } else {
            $remark = '';
        }
        $params = [
            'id'     => $jobId,
            'remark' => $remark,
            'tips'   => 'Tips：简历越完整，被查看的几率越高，更容易受到用人单位的青睐哦～',
        ];
        //判断用户是否关闭了消息通知

        $this->title           = "【投递邀请】“{$job['jobName']}”对您很感兴趣，邀请您投递职位！";
        $this->content         = "{$job['companyName']}对您的简历很感兴趣，邀请您投递“{$job['jobName']}”职位，期待您的回复。";
        $this->innerLink       = BaseMemberMessage::LINK_TYPE_JOB_DETAIL;
        $this->innerLinkParams = $params;

        $this->remindData['resumeId'] = $resumeId;

        // 短信关联的职位信息
        $this->smsType = SmsQueue::TYPE_COMPANY_JOB_INVITE;
        $this->product();

        // noticeTitle需要重新构建
        return [
            'emailId' => $this->emailId,
            'smsId'   => $this->smsId,
        ];
    }

}
