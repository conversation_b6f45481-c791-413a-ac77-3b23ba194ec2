<?php

namespace queue;

use common\base\models\BaseSmsLog;
use common\helpers\DebugHelper;
use common\libs\SmsQueue;
use common\libs\WxWork;
use yii\base\BaseObject;

class SmsJob extends BaseObject implements \yii\queue\JobInterface
{
    public $id;
    public $userType;

    public function execute($queue)
    {
        // 首先把数据拿出来
        $smsLog = BaseSmsLog::findOne($this->id);

        if (!$smsLog) {
            return false;
        }

        if ($smsLog->status != BaseSmsLog::STATUS_WAIT) {
            return false;
        }

        $userType = $this->userType;

        try {
            // 锁定这个,避免重新发送了
            $smsLog->status = BaseSmsLog::STATUS_SENDING;
            $smsLog->save();

            $model = new SmsQueue($smsLog->mobile, $userType, $smsLog->type, $smsLog->mobile_code, $smsLog->ext_params);
            $model->setSmsDetail($smsLog->id, $smsLog);
            $smsLog->status    = BaseSmsLog::STATUS_SUCCESS;
            $content           = $model->send();
            $sendReturnMessage = $model->sendReturnMessage;
            DebugHelper::sms($sendReturnMessage);
            if ($sendReturnMessage && is_array($sendReturnMessage)) {
                // 拿首个数组
                /**
                 * array (
                 * 'qcloud' =>
                 * array (
                 * 'gateway' => 'qcloud',
                 * 'status' => 'success',
                 * 'result' =>
                 * array (
                 * 'result' => 0,
                 * 'errmsg' => 'OK',
                 * 'ext' => '',
                 * 'sid' => '99:43366788417496322430941091',
                 * 'fee' => 1,
                 * 'isocode' => 'CN',
                 * ),
                 * ),
                 * )
                 */

                $data = array_values($sendReturnMessage)[0];
                $sid  = '';
                if ($data['result']['sid']) {
                    // 腾讯云
                    $sid = $data['result']['sid'];
                }
                if ($data['result']['BizId']) {
                    // 阿里云
                    $sid = $data['result']['BizId'];
                }

                $smsLog->sid = $sid;
            }
            $smsLog->content = $content ?: '';

            if ($smsLog->mobile_code && $smsLog->mobile_code != '86') {
                $this->sendMessage($smsLog->mobile_code, $smsLog->mobile);
            }
        } catch (\Exception $e) {
            $smsLog->status = BaseSmsLog::STATUS_FAIL;
            // 这里最好还是给个原因
            $smsLog->reason = $e->getMessage();
        }

        $smsLog->save();

        return true;
    }

    private function sendMessage($code, $mobile)
    {
        return;
        try {
            $message = "海外短信已发送，请相关同事注意。
    > 前缀:<font color='comment'>$code</font>
    > 号码:<font color='comment'>$mobile</font>
    > 短信id:<font color='comment''>$this->id</font>" . PHP_EOL;

            $app = WxWork::getInstance();

            // 剩余的都发送
            $app->robotMessageToSms($message, true);
        } catch (\Exception $e) {
            // 这里就不管了
        }
    }
}